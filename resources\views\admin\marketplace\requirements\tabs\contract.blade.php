@php
    $contract = $contractData ?? (isset($contractVersions) && count($contractVersions) > 0 ? $contractVersions[0] : null);

    // Check if there are any records in school_requirement_contracts table for this requirement ID
    $requirementContracts = \App\SchoolRequirementContract::with(['versions'])
        ->where('requirement_id', $data->id)
        ->get();
    $hasContractData = $requirementContracts->count() > 0;
@endphp

@if($hasContractData)
    @if($contract && $contract['status'] != 'draft')
    <div class="d-flex justify-content-end gap-2 align-items-center mb-3">
        <button type="button" class="mx-3 btn btn-primary contract-status-btn" data-contract-id="{{ $contract['id'] }}" data-status="in_review" @if($contract['status'] === 'in_review') disabled @endif>
            <i class="fas fa-eye"></i> In Review
        </button>
        <button type="button" class="btn btn-primary contract-status-btn" data-contract-id="{{ $contract['id'] }}" data-status="approved" @if($contract['status'] === 'approved') disabled @endif>
            <i class="fas fa-check"></i> Approve
        </button>
    </div>
    @endif
    <div class="card">
        <div class="card-body">
        {{-- Contract Information Form --}}
        <div class="row mb-4">
            <div class="col-md-12">
                <h6 class="mb-3">Contract Information</h6>
                <form id="contractForm">
                    @csrf
                    <input type="hidden" name="requirement_id" value="{{ $data->id }}">
                    @if($contract['status'] != 'draft')
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="legal_first_name">Legal First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="legal_first_name" name="legal_first_name"
                                       value="{{ $contract['legal_first_name'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="legal_last_name">Legal Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="legal_last_name" name="legal_last_name"
                                       value="{{ $contract['legal_last_name'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="email">Email <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="{{ $contract['email'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="phone">Phone <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="phone" name="phone"
                                       value="{{ $contract['phone'] ?? '' }}">
                            </div>
                        </div>
                    </div>
                    @else
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="contractContentForm">Contract Content</label>
                                @php
                                    $contractContent = $schoolContractContent->description ?? '';

                                    // Replace placeholders with actual requirement data when contract status is draft
                                    if (($contract['status'] ?? 'draft') === 'draft' && !empty($data)) {
                                        // Helper function to convert checkbox values to Yes/No
                                        $convertToYesNo = function($value) {
                                            if (is_null($value) || $value === '') return 'N/A';
                                            return (strtolower($value) === 'yes' || $value === '1' || $value === 1) ? 'Yes' : 'No';
                                        };

                                        $replacements = [
                                            // Basic Information
                                            '{{DATE}}' => now()->format('F d, Y'),
                                            '{{ORGANIZATION_NAME}}' => $data->school->school_name ?? 'N/A',
                                            '{{DISTRICT_NAME}}' => $data->school->district->district_name ?? 'N/A',
                                            '{{SCHOOL_NAME}}' => $data->school->school_name ?? 'N/A',
                                            '{{DELIVERY_MODE}}' => $data->delivery_mode ?? 'N/A',
                                            '{{CLASS_TYPE}}' => $data->class_type ?? 'N/A',
                                            '{{COURSE_CODE}}' => $data->subject->course_code ?? 'N/A',
                                            '{{GRADE_LEVELS}}' => implode(', ', $data->grade_level_names ?? []),
                                            '{{CLASS_SIZE}}' => $data->capacity ?? 'N/A',

                                            // Schedule Information
                                            '{{INSTRUCTIONAL_DAYS}}' => $data->no_instrtructional_days ?? 'N/A',
                                            '{{CLASS_DURATION}}' => $data->class_duration ? $data->class_duration . ' hours' : 'N/A',
                                            '{{NON_INSTRUCTIONAL_DAYS}}' => $data->no_non_instructional_hr ?? 'N/A',
                                            '{{START_DATE}}' => $data->start_date ? date('F d, Y', strtotime($data->start_date)) : 'N/A',
                                            '{{END_DATE}}' => $data->end_date ? date('F d, Y', strtotime($data->end_date)) : 'N/A',
                                            '{{SCHEDULE_TYPE}}' => $data->schedule_type ?? 'N/A',
                                            '{{SCHEDULES}}' => $data->getSimpleScheduleDisplay() ?? 'N/A',

                                            // Educator Requirements
                                            '{{SPECIAL_ED_CERT_REQUIRED}}' => $convertToYesNo($data->special_education_certificate),
                                            '{{ESOL_REQUIRED}}' => $convertToYesNo($data->esol_required ?? ''),
                                            '{{TEACHING_LANGUAGES}}' => $data->language_requirements ?? 'N/A',
                                            '{{OTHER_REQUIREMENTS}}' => $data->other_requirements ?? 'N/A',
                                            '{{USE_SCHOOL_LMS}}' => $convertToYesNo($data->will_follow_provided_curriculum),
                                            '{{USE_SCHOOL_SIS}}' => $convertToYesNo($data->provide_schedule_access),

                                            // Cost Information (will be filled from form)
                                            '{{TOTAL_COST}}' => '{{TOTAL_COST}}', // Keep placeholder for form replacement
                                            '{{SETUP_FEE}}' => '{{SETUP_FEE}}', // Keep placeholder for form replacement
                                            '{{SUPPLIES_FEE}}' => '{{SUPPLIES_FEE}}', // Keep placeholder for form replacement
                                            '{{SCHEDULE_CHANGE_FEE}}' => '$100', // Default value
                                            '{{CARD_PROCESSING_FEE}}' => '3%', // Default value
                                            '{{INVOICE_SCHEDULE}}' => '{{INVOICE_SCHEDULE}}', // Keep placeholder for form replacement

                                            // Contact Information (will be filled from form)
                                            '{{LEGEL_USER_NAME}}' => '{{LEGEL_USER_NAME}}', // Keep placeholder for form replacement
                                            '{{LEGEL_USER_TITLE}}' => '{{LEGEL_USER_TITLE}}', // Keep placeholder for form replacement
                                            '{{CLIENT_NAME}}' => $data->school->school_name ?? 'N/A',
                                            '{{CLIENT_ADDRESS}}' => '{{CLIENT_ADDRESS}}', // Keep placeholder for form replacement
                                            '{{CONTACT_EMAIL}}' => '{{CONTACT_EMAIL}}', // Keep placeholder for form replacement
                                            '{{CONTACT_NUMBER}}' => '{{CONTACT_NUMBER}}', // Keep placeholder for form replacement

                                            // Sales Information
                                            '{{SALES_NAME}}' => 'Whizara Sales Team',
                                            '{{SALES_TITLE}}' => 'Sales Representative',
                                            '{{SALES_EMAIL}}' => '<EMAIL>',
                                            '{{SALES_CONTACT_NO}}' => '+1 (555) 123-4567',
                                            '{{SALES_ADDRESS}}' => 'Texas, USA'
                                        ];

                                        $contractContent = str_replace(array_keys($replacements), array_values($replacements), $contractContent);
                                    }
                                @endphp
                                <textarea id="contractContentForm" name="contract_content" class="form-control editor1" rows="10">{{ $contractContent }}</textarea>
                            </div>
                        </div>
                    </div>
                    <div class="g-3 row">
                        @php
                            $startDate = new DateTime($data->start_date);
                            $endDate = new DateTime($data->end_date);

                            $diff = $startDate->diff($endDate);

                            $totalMonths = $diff->y * 12 + $diff->m + ($diff->d > 0 ? 1 : 0);
                        @endphp
                        <div class="col-md-6">
                            <label for="totalFee" class="form-label">Total Fee</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="totalFee" name="TOTAL_FEE" step="0.01" required>
                                <span class="input-group-text">$</span>
                            </div>
                            <small class="form-text text-muted">This amount will be automatically divided across installments</small>
                        </div>
                        <div class="col-md-6">
                            <label for="setupFee" class="form-label">Setup Fee</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="setupFee" name="SETUP_FEE" step="0.01" required>
                                <span class="input-group-text">$</span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label for="suppliesFee" class="form-label">Supplies Fee</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="suppliesFee" name="SUPPLIES_FEE" step="0.01">
                                <span class="input-group-text">$</span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label for="numInstallments" class="form-label">Number of Installments</label>
                            <input type="number" class="form-control" id="numInstallments" min="1" value="{{$totalMonths}}" required>
                            <small class="form-text text-muted">Total fee will be divided equally across installments</small>
                        </div>

                        <!-- Invoice Dates Container -->
                        <div class="col-12" id="installmentDatesContainer">
                            <h5>Invoice Dates for Each Installment</h5>
                            <p class="text-muted mb-3">Installment amounts are automatically calculated based on the total fee. You can manually adjust amounts if needed.</p>
                            <!-- Dynamic date controls will be appended here -->
                        </div>
                    </div>
                    @endif

                    <div class="text-right">
                        @php
                            $hasLegalInfo = !empty($contract['legal_first_name']) && !empty($contract['legal_last_name']) &&
                                          !empty($contract['email']) && !empty($contract['phone']);
                        @endphp

                        @if(!$hasLegalInfo)
                            <!-- Show only legal info update button if legal info is complete -->
                            <button type="button" class="btn btn-success" id="updateLegalInfoBtn">
                                <i class="fas fa-user-check"></i> Update Legal Info
                            </button>
                            <small class="form-text text-muted mt-2">
                                Legal information is complete. You can update contact details here.
                            </small>
                        @else
                            <!-- Show contract save button if legal info is incomplete -->
                            <button type="submit" class="btn btn-primary" id="saveContractBtn">
                                <i class="fas fa-save"></i> <span id="btnText">Save Contract & Generate PDF</span>
                            </button>
                            <button type="button" class="btn btn-success ml-2" id="updateLegalInfoBtn">
                                <i class="fas fa-user-check"></i> Update Legal Info Only
                            </button>
                            <small class="form-text text-muted mt-2">
                                Use "Save Contract & Generate PDF" for full contract with fees, or "Update Legal Info Only" to just update contact details.
                            </small>
                        @endif
                    </div>
                </form>
            </div>
        </div>

        {{-- Contract Versions Section --}}
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">Contract Versions</h6>
                    <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#uploadContractModal">
                        <i class="fas fa-upload"></i> Upload New Version
                    </button>
                </div>

                @if(isset($contractVersions) && count($contractVersions) > 0)
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Version</th>
                                    <th>Document</th>
                                    <th>Notes</th>
                                    <th>Created At</th>
                                    <th>Created By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($contractVersions as $version)
                                    <tr>
                                        <td>
                                            <span class="badge badge-info">{{ $version['version_number'] ?? 'v1.0' }}</span>
                                        </td>
                                        <td>
                                            @if($version['file_url'])
                                                <a href="{{ generateSignedUrl($version['file_url']) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-download"></i> Download
                                                </a>
                                            @else
                                                <span class="text-muted">No file</span>
                                            @endif
                                        </td>
                                        <td>{{ $version['notes'] ?? '-' }}</td>
                                        <td>{{ $version['created_at'] ? \Carbon\Carbon::parse($version['created_at'])->format('M d, Y H:i') : 'N/A' }}</td>
                                        <td>{{ $version['created_by'] }}</td>
                                        <td>
                                            @if($version['file_url'])
                                                <a href="{{ generateSignedUrl($version['file_url']) }}" target="_blank" class="btn btn-sm btn-success">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <div class="mb-3">
                            <i class="fas fa-file-contract fa-3x text-muted"></i>
                        </div>
                        <h6 class="text-muted">No Contract Versions Found</h6>
                        <p class="text-muted">No contract documents have been uploaded for this requirement yet.</p>
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#uploadContractModal">
                            <i class="fas fa-upload"></i> Upload First Contract
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@else
    {{-- No Contract Data Found Section --}}
    <div class="card">
        <div class="card-body">
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-file-contract fa-5x text-muted" style="opacity: 0.3;"></i>
                </div>
                <h4 class="text-muted mb-3">No Contract Data Found</h4>
                <p class="text-muted mb-4">
                    No contract information, content, or versions are available for this requirement ID.
                </p>
            </div>
        </div>
    </div>
@endif

{{-- Upload Contract Modal --}}
<div class="modal fade" id="uploadContractModal" tabindex="-1" role="dialog" aria-labelledby="uploadContractModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadContractModalLabel">Upload Contract Document</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="uploadContractForm" enctype="multipart/form-data">
                <div class="modal-body">
                    @csrf
                    <input type="hidden" name="requirement_id" value="{{ $data->id }}">
                    <input type="hidden" name="contract_id" value="{{ $contract['id'] ?? '' }}">

                    {{-- Copy contract info from main form --}}
                    <input type="hidden" name="legal_first_name" id="modal_legal_first_name">
                    <input type="hidden" name="legal_last_name" id="modal_legal_last_name">
                    <input type="hidden" name="email" id="modal_email">
                    <input type="hidden" name="phone" id="modal_phone">
                    <input type="hidden" name="job_title" value="Contract Administrator">
                    <input type="hidden" name="address" value="N/A">
                    <input type="hidden" name="client_name" value="{{ $data->school->school_name ?? 'N/A' }}">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="version_number">Version Number</label>
                                <input type="text" class="form-control" name="version_number" placeholder="e.g., v1.1, v2.0">
                                <small class="form-text text-muted">Leave empty to auto-increment</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="document">Contract Document <span class="text-danger">*</span></label>
                                <input type="file" class="form-control-file" name="document" accept=".pdf,.doc,.docx,.txt" required>
                                <small class="form-text text-muted">Max size: 20MB. Formats: PDF, DOC, DOCX, TXT</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea class="form-control" name="notes" rows="3" placeholder="Add any notes about this version..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload Contract
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script>
$(document).ready(function() {

    function calculateInstallmentAmount(totalFee, numInstallments, installmentIndex) {
        if (!totalFee || totalFee <= 0 || !numInstallments || numInstallments <= 0) {
            return 0;
        }

        // Calculate base amount per installment
        const baseAmount = totalFee / numInstallments;

        // Handle rounding - distribute any remainder to the first installments
        const remainder = totalFee % numInstallments;

        if (installmentIndex <= remainder) {
            return Math.ceil(baseAmount);
        } else {
            return Math.floor(baseAmount);
        }
    }

    function renderInstallmentDates(num) {
        const container = $('#installmentDatesContainer');
        container.empty();

        // Get total fee value
        const totalFee = parseFloat($('#totalFee').val()) || 0;

        for (let i = 1; i <= num; i++) {
            // Calculate the amount for this installment
            const installmentAmount = calculateInstallmentAmount(totalFee, num, i);

            const html = `
                <div class="row g-3 mb-2 align-items-end installment-row" data-installment="${i}">
                <div class="col-md-4">
                    <label class="form-label">Installment (${i}) Amount ($)</label>
                    <div class="input-group">
                        <input type="number" class="form-control installment-amount" name="installments[${i}][amount]" step="0.01" value="${installmentAmount.toFixed(2)}" required>
                        <span class="input-group-text">$</span>
                    </div>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Invoice Date</label>
                    <input type="date" class="form-control" name="installments[${i}][date]" required>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Invoice Due Date</label>
                    <input type="date" class="form-control" name="installments[${i}][due]" required>
                </div>
                </div>
            `;
            container.append(html);
        }

        // Update total display
        updateInstallmentTotal();
    }

    function updateInstallmentAmounts() {
        const totalFee = parseFloat($('#totalFee').val()) || 0;
        const numInstallments = parseInt($('#numInstallments').val()) || 1;

        $('.installment-amount').each(function(index) {
            const installmentIndex = index + 1;
            const amount = calculateInstallmentAmount(totalFee, numInstallments, installmentIndex);
            $(this).val(amount.toFixed(2));
        });

        updateInstallmentTotal();
    }

    function updateInstallmentTotal() {
        let total = 0;
        $('.installment-amount').each(function() {
            total += parseFloat($(this).val()) || 0;
        });

        // Show total if there are installments
        if ($('.installment-amount').length > 0) {
            let totalDisplay = $('#installmentTotal');
            if (totalDisplay.length === 0) {
                $('#installmentDatesContainer').append(`
                    <div class="row mt-3" id="installmentTotal">
                        <div class="col-md-4">
                            <div class="alert alert-info">
                                <strong>Total Installments: $<span id="totalAmount">${total.toFixed(2)}</span></strong>
                            </div>
                        </div>
                    </div>
                `);
            } else {
                $('#totalAmount').text(total.toFixed(2));
            }
        }
    }

    // Render initial installment
    renderInstallmentDates(parseInt($('#numInstallments').val()));

    // Update dynamically on number of installments change
    $('#numInstallments').on('input', function() {
        const num = parseInt($(this).val()) || 1;
        renderInstallmentDates(num);
    });

    // Update installment amounts when total fee changes
    $('#totalFee').on('input', function() {
        updateInstallmentAmounts();
    });

    // Handle manual changes to installment amounts
    $(document).on('input', '.installment-amount', function() {
        updateInstallmentTotal();
    });

    // Update button text and visibility based on legal information
    function updateButtonText() {
        const hasLegalInfo = $('#legal_first_name').val() && $('#legal_last_name').val() &&
                           $('#email').val() && $('#phone').val();

        // Only update if the button exists (it might be hidden based on server-side condition)
        if ($('#btnText').length) {
            if (hasLegalInfo) {
                $('#btnText').text('Finalize Contract & Generate PDF');
            } else {
                $('#btnText').text('Save Draft & Generate PDF');
            }
        }
    }

    // Monitor legal fields for changes
    $('#legal_first_name, #legal_last_name, #email, #phone').on('input', function() {
        updateButtonText();

        // Optionally, you could add logic here to show/hide buttons dynamically
        // based on real-time changes, but for now we'll rely on server-side rendering
    });

    // Initial button text update
    updateButtonText();

    // Handle legal info only update
    $('#updateLegalInfoBtn').on('click', function(e) {
        e.preventDefault();

        const requirementId = $('input[name="requirement_id"]').val();
        if (!requirementId) {
            alertify.error('Requirement ID not found');
            return;
        }

        const formData = new FormData();
        formData.append('requirement_id', requirementId);
        formData.append('legal_first_name', $('#legal_first_name').val());
        formData.append('legal_last_name', $('#legal_last_name').val());
        formData.append('email', $('#email').val());
        formData.append('phone', $('#phone').val());

        $.ajax({
            url: '{{ route("admin.marketplace-updateLegalInfo") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    alertify.success(response.message);

                    // Check if legal info is now complete
                    const hasLegalInfo = $('#legal_first_name').val() && $('#legal_last_name').val() &&
                                       $('#email').val() && $('#phone').val();

                    if (hasLegalInfo) {
                        // If legal info is now complete, refresh the page to show the correct button state
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        // Update button text based on new status
                        updateButtonText();
                    }
                } else {
                    alertify.error(response.message || 'Failed to update legal information');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error updating legal info:', error);
                let errorMessage = 'Failed to update legal information';

                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    const errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMessage = errors.join(', ');
                }

                alertify.error(errorMessage);
            }
        });
    });

    // Validate that total installments match total fee
    function validateInstallmentTotal() {
        const totalFee = parseFloat($('#totalFee').val()) || 0;
        let installmentTotal = 0;

        $('.installment-amount').each(function() {
            installmentTotal += parseFloat($(this).val()) || 0;
        });

        const difference = Math.abs(totalFee - installmentTotal);

        if (difference > 0.01) { // Allow for small rounding differences
            $('#installmentTotal').removeClass('alert-info').addClass('alert-warning');
            $('#installmentTotal .alert').html(`
                <strong>Warning: Total Installments ($${installmentTotal.toFixed(2)}) doesn't match Total Fee ($${totalFee.toFixed(2)})</strong>
            `);
            return false;
        } else {
            $('#installmentTotal').removeClass('alert-warning').addClass('alert-info');
            $('#installmentTotal .alert').html(`
                <strong>Total Installments: $<span id="totalAmount">${installmentTotal.toFixed(2)}</span></strong>
            `);
            return true;
        }
    }

    // Add validation on form submit
    $('#contractForm').on('submit', function(e) {
        if (!validateInstallmentTotal()) {
            e.preventDefault();
            alertify.error('Please ensure installment amounts match the total fee');
            return false;
        }
    });
    // Handle contract status button clicks
    $('.contract-status-btn').on('click', function() {
        const contractId = $(this).data('contract-id');
        const status = $(this).data('status');

        // Check if updateContractStatus function exists
        if (typeof window.updateContractStatus === 'function') {
            window.updateContractStatus(contractId, status);
        } else {
            console.error('updateContractStatus function not found');
            alertify.error('Contract status update function not available. Please refresh the page.');
        }
    });

    // Handle contract form submission (updated to include installment validation)
    $('#contractForm').off('submit').on('submit', function(e) {
        e.preventDefault();

        // Validate installment totals first
        if (!validateInstallmentTotal()) {
            alertify.error('Please ensure installment amounts match the total fee');
            return false;
        }

        // Update CKEditor instances
        for (instance in CKEDITOR.instances) {
            CKEDITOR.instances[instance].updateElement();
        }

        const formData = new FormData(this);
        const requirementId = $('input[name="requirement_id"]').val();

        // Add fee information to form data
        formData.append('TOTAL_FEE', $('#totalFee').val());
        formData.append('SETUP_FEE', $('#setupFee').val() || 0);
        formData.append('SUPPLIES_FEE', $('#suppliesFee').val() || 0);

        // Add installment data to form data
        $('.installment-row').each(function(index) {
            const installmentIndex = $(this).data('installment');
            const amount = $(this).find('.installment-amount').val();
            const date = $(this).find('input[type="date"]:first').val();
            const due = $(this).find('input[type="date"]:last').val();

            formData.append(`installments[${installmentIndex}][amount]`, amount);
            formData.append(`installments[${installmentIndex}][date]`, date);
            formData.append(`installments[${installmentIndex}][due]`, due);
        });

        // Validate contract content from CKEditor (temporarily disabled for debugging)
        let contractContent = '';
        if (CKEDITOR.instances['contractContentForm']) {
            contractContent = CKEDITOR.instances['contractContentForm'].getData();
        } else {
            contractContent = $('#contractContentForm').val();
        }

        // Debug: Log the contract content to see what's being retrieved
        console.log('Contract content:', contractContent);

        // Temporarily disable strict validation - allow saving with minimal content
        // This will help identify if the issue is with content validation or something else
        if (!contractContent) {
            console.log('No contract content found, but allowing save for debugging');
            // alertify.error('Please enter contract content before updating');
            // return;
        }

        // Legal fields are now optional - contract can be saved as draft without them
        const legalFirstName = $('#legal_first_name').val();
        const legalLastName = $('#legal_last_name').val();
        const email = $('#email').val();
        const phone = $('#phone').val();

        // Show info message if legal info is not provided
        const hasLegalInfo = legalFirstName && legalLastName && email && phone;
        if (!hasLegalInfo) {
            console.log('Contract will be saved as draft - legal information can be added later');
        }

        // Validate fee fields
        const totalFee = parseFloat($('#totalFee').val());
        const setupFee = parseFloat($('#setupFee').val()) || 0;
        const suppliesFee = parseFloat($('#suppliesFee').val()) || 0;

        if (!totalFee || totalFee <= 0) {
            alertify.error('Please enter a valid total fee');
            return;
        }

        // Validate installment fields
        let installmentValidation = true;
        $('.installment-row').each(function() {
            const amount = $(this).find('.installment-amount').val();
            const date = $(this).find('input[type="date"]:first').val();
            const due = $(this).find('input[type="date"]:last').val();

            if (!amount || !date || !due) {
                installmentValidation = false;
                return false; // Break out of each loop
            }
        });

        if (!installmentValidation) {
            alertify.error('Please fill in all installment fields (amount, invoice date, and due date)');
            return;
        }

        $.ajax({
            url: '{{ route("admin.marketplace-updateContractContent") }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    const hasLegalInfo = $('#legal_first_name').val() && $('#legal_last_name').val() &&
                                       $('#email').val() && $('#phone').val();

                    if (hasLegalInfo) {
                        alertify.success('Contract information updated and PDF generated successfully');
                    } else {
                        alertify.success('Contract saved as draft. Add legal contact information to finalize.');
                    }
                } else {
                    alertify.error(response.message || 'Failed to update contract information');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error updating contract information:', error);
                alertify.error('Failed to update contract information. Please try again.');
            }
        });
    });
});
</script>