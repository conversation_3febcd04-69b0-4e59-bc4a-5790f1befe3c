<?php

namespace App\V2\Core\Services;

use App\Models\v1\EmailScheduler;
use App\Models\v1\EmailTemplate;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
use App\Mail\GenericEmail;

class EmailSchedulerService
{
    /**
     * Immediate trigger fired from controller/service.
     */
    public function triggerImmediate(string $event, array $context = []): void
    {
        $schedules = EmailScheduler::with('templates', 'templates.layout')->where('event_name', $event)
            ->where('status', 'enabled')
            ->get();

        foreach ($schedules as $schedule) {
            if ($this->evaluateConditions($schedule, $context)) {
                $this->dispatchEmails($schedule, $context);
                $this->markTriggered($schedule);
            }
        }
    }

    /**
     * Scheduled triggers via Kernel cron.
     */
    public function runScheduled(): void
    {
        $schedules = EmailScheduler::with('templates', 'templates.layout')->where('is_active', true)
            ->where('trigger_type', 'scheduled')
            ->get();

        foreach ($schedules as $schedule) {
            if ($this->shouldRun($schedule) && $this->evaluateConditions($schedule)) {
                $this->dispatchEmails($schedule);
                $this->markTriggered($schedule);
            }
        }
    }

    /**
     * Check if a schedule should run now based on recurrence.
     */
    protected function shouldRun(EmailScheduler $schedule): bool
    {
        $now = now();
        // $lastRun = $schedule->last_triggered_at;

        switch ($schedule->recurrence) {
            case 'daily':
                return !$lastRun || $now->diffInDays($lastRun) >= 1;
            case 'weekly':
                return !$lastRun || $now->diffInWeeks($lastRun) >= 1;
            case 'monthly':
                return !$lastRun || $now->diffInMonths($lastRun) >= 1;
            case 'once':
                return true; // only run once
            default:
                return true;
        }
    }

    /**
     * Evaluate conditions (supports AND / OR chaining)
     */
    protected function evaluateConditions(EmailScheduler $schedule, array $context = []): bool
    {
        $conditions = $schedule->conditions ?? [];
        if (empty($conditions) || count($conditions) == 0) return true;

        $result = null;

        foreach ($conditions as $index => $rule) {
            $value = $this->resolveConditionValue($schedule, $rule['field'], $context);
            $currentCheck = $this->applyOperatorCheck($value, $rule['operator'], $rule['value']);

            if ($index === 0) {
                $result = $currentCheck;
            } else {
                $logic = strtoupper($rule['logical_operator'] ?? 'AND');
                $result = ($logic === 'AND') ? ($result && $currentCheck) : ($result || $currentCheck);
            }
        }

        return (bool) $result;
    }

    /**
     * Apply operator check for condition.
     */
    protected function applyOperatorCheck($value, string $operator, $expected): bool
    {
        switch ($operator) {
            case '=': return $value == $expected;
            case '!=': return $value != $expected;
            case '>': return $value > $expected;
            case '<': return $value < $expected;
            case '>=': return $value >= $expected;
            case '<=': return $value <= $expected;
            case 'past_days': return Carbon::parse($value)->diffInDays(now()) >= (int)$expected;
            case 'future_days': return Carbon::parse($value)->diffInDays(now()) <= (int)$expected;
            default: return false;
        }
    }

    /**
     * Resolve condition value dynamically (context overrides DB).
     */
    protected function resolveConditionValue(EmailScheduler $schedule, string $field, array $context = [])
    {
        if (isset($context[$field])) return $context[$field];

        if (strpos($field, '.') !== false) {
            [$modelAlias, $attribute] = explode('.', $field);
            $modelClass = config("email_scheduler.models.$modelAlias");

            if ($modelClass && class_exists($modelClass)) {
                $instance = $modelClass::first(); // replace with actual lookup logic
                return $instance->$attribute ?? null;
            }
        }

        return null;
    }

    /**
     * Dispatch emails with TO and CC support using queued mailables.
     */
    protected function dispatchEmails(EmailScheduler $schedule, array $context = []): void
    {

        $recipients = $schedule->recipients_to ?? [];
        $ccRecipients = $schedule->recipients_cc ?? [];
        $templates = $schedule->templates;

        foreach ($templates as $template) {

            $toEmails = [];
            foreach ($recipients as $recipient) {
                $toEmails = array_merge($toEmails, $this->resolveRecipients($recipient, $context));
            }

            $ccEmails = [];
            foreach ($ccRecipients as $cc) {
                $ccEmails = array_merge($ccEmails, $this->resolveRecipients($cc, $context));
            }

            // Remove duplicates & nulls
            $toEmails = array_filter(array_unique($toEmails));
            $ccEmails = array_filter(array_unique($ccEmails));

            foreach ($toEmails as $toEmail) {
                if (!$toEmail) continue;

                try {
                    Mail::to($toEmail)
                        ->cc($ccEmails)
                        ->queue(new GenericEmail($template, $context));
                } catch (\Exception $e) {
                    Log::error("EmailScheduler: Failed queuing email to $toEmail", [
                        'schedule_id' => $schedule->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }
    }

    /**
     * Resolve recipients dynamically (cached for heavy queries)
     */
    protected function resolveRecipients(string $recipient, array $context = []): array
    {
        switch ($recipient) {
            case 'all_educator':
                return Cache::remember('all_educator_emails', 300, function () {
                    return \App\Models\v1\OnboardingInstructor::pluck('email')->toArray();
                });
            case 'all_schools':
                return Cache::remember('all_school_emails', 300, function () {
                    return \App\Models\v1\SchoolUser::pluck('email')->toArray();
                });
            case 'admin:super_admin':
                return [];
            case 'school:proctor':
                return [];
            case 'educator':
                return [$context['educator']->email ?? null];
            case 'school_user':
                $schoolUser = $context['school_user'] ?? null;
                if (!$schoolUser) return [];

                // Handle both single user and collection
                if ($schoolUser instanceof \Illuminate\Support\Collection) {
                    return $schoolUser->pluck('email')->filter()->toArray();
                }

                return [$schoolUser->email];
            default:
                return [];
        }
    }

    /**
     * Mark schedule as triggered by updating last_triggered_at.
     */
    protected function markTriggered(EmailScheduler $schedule): void
    {
        // $schedule->last_triggered_at = now();
        $schedule->save();
    }
}
