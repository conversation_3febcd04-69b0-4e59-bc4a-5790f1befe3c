@extends('admin.layouts.master')

@section('title')
    Email Schedule Details | Whizara
@endsection

@section('content')
    <!-- MAIN SECTION START -->
    <main class="content">
        <div class="container-fluid p-0">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url('admin/k12connections/manage-email-schedules') }}">Email Schedules</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $schedule->name }}</li>
                </ol>
            </nav>

            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Email Schedule Details</h5>
                            <div>
                                <a href="{{ url('admin/k12connections/manage-email-schedules/' . $schedule->id . '/edit') }}"
                                   class="btn btn-primary btn-sm">
                                    <i class="fa fa-edit"></i> Edit
                                </a>
                                @if($schedule->status === 'enabled')
                                <button class="btn btn-warning btn-sm" id="testTriggerBtn" data-id="{{ $schedule->id }}">
                                    <i class="fa fa-play"></i> Test Trigger
                                </button>
                                @endif
                                <a href="{{ url('admin/k12connections/manage-email-schedules') }}"
                                   class="btn btn-secondary btn-sm">
                                    <i class="fa fa-arrow-left"></i> Back
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Basic Information -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6 class="text-muted">Basic Information</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Name:</strong></td>
                                            <td>{{ $schedule->name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <span class="badge badge-{{ $schedule->status == 'enabled' ? 'success' : 'secondary' }}">
                                                    {{ ucfirst($schedule->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Recurrence:</strong></td>
                                            <td>{{ ucfirst($schedule->recurrence) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Event Name:</strong></td>
                                            <td>{{ $schedule->event_name ? ucwords(str_replace('_', ' ', $schedule->event_name)) : 'N/A' }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted">System Information</h6>
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Created By:</strong></td>
                                            <td>{{ $schedule->createdBy->name ?? 'System' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Created At:</strong></td>
                                            <td>{{ $schedule->created_at->format('M d, Y H:i A') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Updated At:</strong></td>
                                            <td>{{ $schedule->updated_at->format('M d, Y H:i A') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <!-- Recipients -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6 class="text-muted">Recipients (To)</h6>
                                    @if($schedule->recipients_to && count($schedule->recipients_to) > 0)
                                        <ul class="list-unstyled">
                                            @foreach($schedule->recipients_to as $recipient)
                                                <li><span class="badge badge-info">{{ ucwords(str_replace('_', ' ', $recipient)) }}</span></li>
                                            @endforeach
                                        </ul>
                                    @else
                                        <p class="text-muted">No recipients specified</p>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-muted">Recipients (CC)</h6>
                                    @if($schedule->recipients_cc && count($schedule->recipients_cc) > 0)
                                        <ul class="list-unstyled">
                                            @foreach($schedule->recipients_cc as $recipient)
                                                <li><span class="badge badge-secondary">{{ ucwords(str_replace('_', ' ', $recipient)) }}</span></li>
                                            @endforeach
                                        </ul>
                                    @else
                                        <p class="text-muted">No CC recipients specified</p>
                                    @endif
                                </div>
                            </div>

                            <!-- Email Templates -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-muted">Email Templates</h6>
                                    @if($schedule->templates && $schedule->templates->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Title</th>
                                                        <th>Subject</th>
                                                        <th>Layout</th>
                                                        <th>Tags</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($schedule->templates as $template)
                                                        <tr>
                                                            <td>{{ $template->title }}</td>
                                                            <td>{{ $template->subject }}</td>
                                                            <td>{{ $template->layout->title ?? 'No Layout' }}</td>
                                                            <td>
                                                                @if($template->tags)
                                                                    @foreach($template->tags as $tag)
                                                                        <span class="badge badge-light">{{ $tag }}</span>
                                                                    @endforeach
                                                                @else
                                                                    <span class="text-muted">No tags</span>
                                                                @endif
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <p class="text-muted">No email templates assigned</p>
                                    @endif
                                </div>
                            </div>

                            <!-- Conditions -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-muted">Conditions</h6>
                                    @if($schedule->conditions && $schedule->conditions->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>Model</th>
                                                        <th>Field</th>
                                                        <th>Operator</th>
                                                        <th>Value</th>
                                                        <th>Logic</th>
                                                        <th>Description</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($schedule->conditions as $condition)
                                                        <tr>
                                                            <td>{{ $condition->model_name }}</td>
                                                            <td>{{ $condition->field_name }}</td>
                                                            <td><code>{{ $condition->operator }}</code></td>
                                                            <td>{{ $condition->value }}</td>
                                                            <td>{{ $condition->logical_operator }}</td>
                                                            <td>{{ $condition->description ?? 'N/A' }}</td>
                                                            <td>
                                                                <span class="badge badge-{{ $condition->active ? 'success' : 'secondary' }}">
                                                                    {{ $condition->active ? 'Active' : 'Inactive' }}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <p class="text-muted">No conditions specified</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <!-- MAIN SECTION END -->
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Handle test trigger button
    $('#testTriggerBtn').on('click', function() {
        const scheduleId = $(this).data('id');
        const btn = $(this);
        const originalText = btn.html();

        if (confirm('Are you sure you want to test trigger this email schedule?')) {
            btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Triggering...');

            $.ajax({
                url: `{{ url('admin/k12connections/manage-email-schedules') }}/${scheduleId}/test-trigger`,
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    context: {} // You can add test context data here
                },
                success: function(response) {
                    if (response.success) {
                        alertify.success(response.message);
                    } else {
                        alertify.error(response.message || 'Failed to trigger email schedule');
                    }
                },
                error: function(xhr) {
                    let message = 'An error occurred while triggering the email schedule';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }
                    alertify.error(message);
                },
                complete: function() {
                    btn.prop('disabled', false).html(originalText);
                }
            });
        }
    });
});
</script>
@endsection
